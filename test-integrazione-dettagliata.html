<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Integrazione Dati Dettagliati - Smorf-IA</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: #f7fafc;
        }
        .test-section h3 {
            color: #2d3748;
            margin-top: 0;
        }
        .input-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #4a5568;
        }
        input, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        input:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 5px;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #edf2f7;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .comparison-item {
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
        }
        .standard { background: #fed7d7; border-color: #fc8181; }
        .detailed { background: #c6f6d5; border-color: #68d391; }
        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 600;
        }
        .status.loading { background: #fef5e7; color: #d69e2e; }
        .status.success { background: #c6f6d5; color: #38a169; }
        .status.error { background: #fed7d7; color: #e53e3e; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            font-size: 14px;
            color: #718096;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Integrazione Dati Dettagliati</h1>
        
        <div class="test-section">
            <h3>📊 Stato Sistema</h3>
            <div id="system-status" class="status loading">Caricamento in corso...</div>
            <div class="stats" id="stats-container"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Test Ricerca Parola</h3>
            <div class="input-group">
                <label for="search-word">Parola da cercare:</label>
                <input type="text" id="search-word" placeholder="es. abate, acqua, casa..." value="abate">
            </div>
            <button onclick="testWordSearch()">🔍 Cerca Parola</button>
            <div class="comparison" id="word-comparison"></div>
        </div>

        <div class="test-section">
            <h3>📝 Test Analisi Testo</h3>
            <div class="input-group">
                <label for="test-text">Testo da analizzare:</label>
                <textarea id="test-text" rows="3" placeholder="Inserisci un testo per testare l'analisi...">Ho sognato un abate in aeroplano che beveva acqua calda</textarea>
            </div>
            <button onclick="testTextAnalysis()">📝 Analizza Testo</button>
            <div class="comparison" id="text-comparison"></div>
        </div>

        <div class="test-section">
            <h3>🎯 Test Matching Simboli</h3>
            <div class="input-group">
                <label for="test-symbols">Simboli (separati da virgola):</label>
                <input type="text" id="test-symbols" placeholder="es. abate, aeroplano, acqua" value="abate, aeroplano, acqua">
            </div>
            <button onclick="testSymbolMatching()">🎯 Test Matching</button>
            <div class="comparison" id="symbol-comparison"></div>
        </div>

        <div class="test-section">
            <h3>⚡ Test Performance</h3>
            <button onclick="runPerformanceTest()">⚡ Esegui Test Performance</button>
            <div id="performance-results" class="results" style="display: none;"></div>
        </div>
    </div>

    <script type="module">
        import { SmorfiaService } from './js/smorfia.js';
        import { smorfiaUtils } from './js/smorfia-utils.js';

        let smorfiaService;
        let systemReady = false;

        // Inizializzazione
        async function initSystem() {
            try {
                document.getElementById('system-status').textContent = 'Inizializzazione SmorfiaService...';
                
                smorfiaService = new SmorfiaService();
                await smorfiaService.init();
                
                document.getElementById('system-status').textContent = 'Collegamento SmorfiaUtils...';
                smorfiaService.setSmorfiaUtils(smorfiaUtils);
                
                // Attendi che SmorfiaUtils carichi i dati dettagliati
                let attempts = 0;
                while (!smorfiaUtils.isDetailedDataLoaded() && attempts < 50) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }
                
                updateSystemStatus();
                systemReady = true;
                
            } catch (error) {
                console.error('Errore inizializzazione:', error);
                document.getElementById('system-status').className = 'status error';
                document.getElementById('system-status').textContent = 'Errore inizializzazione: ' + error.message;
            }
        }

        function updateSystemStatus() {
            const statusEl = document.getElementById('system-status');
            const statsContainer = document.getElementById('stats-container');
            
            const smorfiaLoaded = smorfiaService && smorfiaService.isLoaded;
            const utilsLoaded = smorfiaUtils.isDataLoaded();
            const detailedLoaded = smorfiaUtils.isDetailedDataLoaded();
            
            if (smorfiaLoaded && utilsLoaded) {
                statusEl.className = 'status success';
                statusEl.textContent = '✅ Sistema completamente caricato e operativo';
                
                // Mostra statistiche
                const stats = smorfiaUtils.getStatistiche();
                const detailedLetters = detailedLoaded ? Object.keys(smorfiaUtils.detailedSmorfiaData).length : 0;
                
                statsContainer.innerHTML = `
                    <div class="stat-card">
                        <div class="stat-value">${smorfiaLoaded ? '✅' : '❌'}</div>
                        <div class="stat-label">Database AI</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${stats.totaleParole || 0}</div>
                        <div class="stat-label">Parole Totali</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${detailedLetters}/20</div>
                        <div class="stat-label">Lettere Dettagliate</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${detailedLoaded ? '✅' : '❌'}</div>
                        <div class="stat-label">Dati Dettagliati</div>
                    </div>
                `;
            } else {
                statusEl.className = 'status loading';
                statusEl.textContent = 'Caricamento in corso...';
            }
        }

        // Test ricerca parola
        window.testWordSearch = function() {
            if (!systemReady) {
                alert('Sistema non ancora pronto');
                return;
            }
            
            const word = document.getElementById('search-word').value.trim();
            if (!word) return;
            
            const standardResults = smorfiaUtils.cercaParola(word);
            const detailedResults = smorfiaUtils.cercaParolaDettagliata(word);
            
            document.getElementById('word-comparison').innerHTML = `
                <div class="comparison-item standard">
                    <h4>🔍 Ricerca Standard</h4>
                    <p><strong>Risultati trovati:</strong> ${standardResults.length}</p>
                    <p><strong>Numeri:</strong> ${standardResults.join(', ') || 'Nessuno'}</p>
                </div>
                <div class="comparison-item detailed">
                    <h4>🔍 Ricerca Dettagliata</h4>
                    <p><strong>Risultati trovati:</strong> ${detailedResults.length}</p>
                    ${detailedResults.map(r => `
                        <div style="margin: 10px 0; padding: 8px; background: rgba(255,255,255,0.5); border-radius: 4px;">
                            <strong>${r.nome}</strong><br>
                            Numeri: ${r.numeri.join(', ')}<br>
                            Tipo: ${r.tipo}<br>
                            Lettera: ${r.lettera}
                        </div>
                    `).join('')}
                </div>
            `;
        };

        // Test analisi testo
        window.testTextAnalysis = function() {
            if (!systemReady) {
                alert('Sistema non ancora pronto');
                return;
            }
            
            const text = document.getElementById('test-text').value.trim();
            if (!text) return;
            
            const standardResults = smorfiaUtils.analizzaTesto(text);
            const detailedResults = smorfiaUtils.analizzaTestoDettagliato(text);
            
            document.getElementById('text-comparison').innerHTML = `
                <div class="comparison-item standard">
                    <h4>📝 Analisi Standard</h4>
                    <p><strong>Match trovati:</strong> ${standardResults.length}</p>
                    ${standardResults.map(r => `
                        <div style="margin: 5px 0;">
                            <strong>${r.parola}:</strong> ${r.numeri.join(', ')}
                        </div>
                    `).join('')}
                </div>
                <div class="comparison-item detailed">
                    <h4>📝 Analisi Dettagliata</h4>
                    <p><strong>Match trovati:</strong> ${detailedResults.length}</p>
                    ${detailedResults.map(r => `
                        <div style="margin: 10px 0; padding: 8px; background: rgba(255,255,255,0.5); border-radius: 4px;">
                            <strong>${r.elemento}</strong><br>
                            Numeri: ${r.numeri.join(', ')}<br>
                            Tipo: ${r.tipo}<br>
                            Lettera: ${r.lettera}
                        </div>
                    `).join('')}
                </div>
            `;
        };

        // Test matching simboli
        window.testSymbolMatching = function() {
            if (!systemReady) {
                alert('Sistema non ancora pronto');
                return;
            }
            
            const symbolsText = document.getElementById('test-symbols').value.trim();
            if (!symbolsText) return;
            
            const symbols = symbolsText.split(',').map(s => s.trim());
            
            const standardMatches = smorfiaService.findNumbers(symbols);
            const detailedMatches = smorfiaService.findNumbersDetailed(symbols);
            
            document.getElementById('symbol-comparison').innerHTML = `
                <div class="comparison-item standard">
                    <h4>🎯 Matching Standard</h4>
                    <p><strong>Match trovati:</strong> ${standardMatches.length}</p>
                    <p><strong>Numeri:</strong> ${standardMatches.map(m => m.numero).join(', ')}</p>
                    ${standardMatches.map(m => `
                        <div style="margin: 5px 0;">
                            <strong>${m.numero}:</strong> ${m.significato}
                        </div>
                    `).join('')}
                </div>
                <div class="comparison-item detailed">
                    <h4>🎯 Matching Dettagliato</h4>
                    <p><strong>Match trovati:</strong> ${detailedMatches.length}</p>
                    <p><strong>Numeri:</strong> ${detailedMatches.map(m => m.numero).join(', ')}</p>
                    ${detailedMatches.map(m => `
                        <div style="margin: 10px 0; padding: 8px; background: rgba(255,255,255,0.5); border-radius: 4px;">
                            <strong>${m.numero}:</strong> ${m.significato}<br>
                            ${m.matchType ? `Tipo: ${m.matchType}<br>` : ''}
                            ${m.dettagliVarianti ? `Varianti: ${m.dettagliVarianti.length}<br>` : ''}
                            ${m.keyword ? `Keyword: ${m.keyword}` : ''}
                        </div>
                    `).join('')}
                </div>
            `;
        };

        // Test performance
        window.runPerformanceTest = async function() {
            if (!systemReady) {
                alert('Sistema non ancora pronto');
                return;
            }
            
            const resultsEl = document.getElementById('performance-results');
            resultsEl.style.display = 'block';
            resultsEl.innerHTML = 'Esecuzione test performance...';
            
            const testWords = ['abate', 'acqua', 'casa', 'amore', 'morte', 'cane', 'fuoco', 'mare', 'volare', 'soldi'];
            const testText = 'Ho sognato un abate in aeroplano che beveva acqua calda mentre guardava il mare';
            
            // Test ricerca parole
            const startStandard = performance.now();
            for (let i = 0; i < 100; i++) {
                testWords.forEach(word => smorfiaUtils.cercaParola(word));
            }
            const endStandard = performance.now();
            
            const startDetailed = performance.now();
            for (let i = 0; i < 100; i++) {
                testWords.forEach(word => smorfiaUtils.cercaParolaDettagliata(word));
            }
            const endDetailed = performance.now();
            
            // Test analisi testo
            const startTextStandard = performance.now();
            for (let i = 0; i < 50; i++) {
                smorfiaUtils.analizzaTesto(testText);
            }
            const endTextStandard = performance.now();
            
            const startTextDetailed = performance.now();
            for (let i = 0; i < 50; i++) {
                smorfiaUtils.analizzaTestoDettagliato(testText);
            }
            const endTextDetailed = performance.now();
            
            resultsEl.innerHTML = `
                <h4>📊 Risultati Performance</h4>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-value">${(endStandard - startStandard).toFixed(2)}ms</div>
                        <div class="stat-label">Ricerca Standard (1000 ops)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${(endDetailed - startDetailed).toFixed(2)}ms</div>
                        <div class="stat-label">Ricerca Dettagliata (1000 ops)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${(endTextStandard - startTextStandard).toFixed(2)}ms</div>
                        <div class="stat-label">Analisi Testo Standard (50 ops)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${(endTextDetailed - startTextDetailed).toFixed(2)}ms</div>
                        <div class="stat-label">Analisi Testo Dettagliata (50 ops)</div>
                    </div>
                </div>
                <p><strong>Overhead Dettagliato:</strong> 
                   Ricerca: +${((endDetailed - startDetailed) - (endStandard - startStandard)).toFixed(2)}ms, 
                   Analisi: +${((endTextDetailed - startTextDetailed) - (endTextStandard - startTextStandard)).toFixed(2)}ms
                </p>
            `;
        };

        // Avvia inizializzazione
        initSystem();
    </script>
</body>
</html>
