/* SMORFIA.JS - Database e logica della smorfia napoletana */

export class SmorfiaService {
  constructor() {
    this.smorfiaData = null;
    this.isLoaded = false;
    this.smorfiaUtils = null; // Riferimento a SmorfiaUtils per dati dettagliati
  }

  async init() {
    try {
      const response = await fetch('./data/smorfia-ai-ottimizzata.json');
      const data = await response.json();
      this.smorfiaData = data.smorfia;
      this.isLoaded = true;
      console.log('✅ Database smorfia AI ottimizzato caricato');
    } catch (error) {
      console.error('❌ Errore caricamento smorfia AI ottimizzata:', error);
      throw new Error('Impossibile caricare il database della smorfia AI ottimizzata');
    }
  }

  /**
   * Imposta il riferimento a SmorfiaUtils per accedere ai dati dettagliati
   */
  setSmorfiaUtils(smorfiaUtils) {
    this.smorfiaUtils = smorfiaUtils;
    console.log('🔗 SmorfiaUtils collegato al SmorfiaService');
  }

  findNumbers(keywords) {
    if (!this.isLoaded || !Array.isArray(keywords)) return [];
    const matches = [];
    const keywordsLower = keywords.map(k => k.toLowerCase());

    for (const [numero, data] of Object.entries(this.smorfiaData)) {
      const simboliLower = data.simboli.map(s => s.toLowerCase());
      const significatoLower = data.significato.toLowerCase();
      const hasMatch = keywordsLower.some(keyword =>
        simboliLower.some(simbolo => simbolo.includes(keyword)) ||
        significatoLower.includes(keyword)
      );
      if (hasMatch) {
        matches.push({
          numero: parseInt(numero),
          significato: data.significato,
          simboli: data.simboli,
          descrizione: data.descrizione
        });
      }
    }
    return matches;
  }

  /**
   * Trova numeri utilizzando anche i dati dettagliati con varianti
   * @param {Array} keywords - Array di parole chiave
   * @returns {Array} Array di match con informazioni dettagliate
   */
  findNumbersDetailed(keywords) {
    if (!this.isLoaded || !Array.isArray(keywords)) return [];

    // Prima cerca con il metodo standard
    const standardMatches = this.findNumbers(keywords);

    // Se abbiamo SmorfiaUtils, cerca anche nei dati dettagliati
    if (this.smorfiaUtils && this.smorfiaUtils.isDetailedDataLoaded()) {
      const detailedMatches = [];

      keywords.forEach(keyword => {
        const risultatiDettagliati = this.smorfiaUtils.getNumeriConVarianti(keyword);

        risultatiDettagliati.tuttiINumeri.forEach(numero => {
          const numeroInfo = this.getNumberInfo(numero);
          if (numeroInfo) {
            // Trova i dettagli specifici per questo numero
            const dettagliSpecifici = risultatiDettagliati.dettagli.filter(d =>
              d.numeri.includes(numero)
            );

            detailedMatches.push({
              numero: numero,
              significato: numeroInfo.significato,
              simboli: numeroInfo.simboli,
              descrizione: numeroInfo.descrizione,
              matchType: 'detailed',
              dettagliVarianti: dettagliSpecifici,
              keyword: keyword
            });
          }
        });
      });

      // Combina i risultati, rimuovendo duplicati
      const allMatches = [...standardMatches];
      detailedMatches.forEach(detailedMatch => {
        const exists = allMatches.find(m => m.numero === detailedMatch.numero);
        if (!exists) {
          allMatches.push(detailedMatch);
        } else {
          // Arricchisci il match esistente con i dettagli
          exists.dettagliVarianti = detailedMatch.dettagliVarianti;
          exists.matchType = 'enhanced';
        }
      });

      return allMatches;
    }

    return standardMatches;
  }

  getNumberInfo(numero) {
    if (!this.isLoaded) {
      console.warn('⚠️ Database smorfia non ancora caricato');
      return null;
    }

    const numeroStr = numero.toString();
    const data = this.smorfiaData[numeroStr];

    if (!data) {
      console.warn(`⚠️ Numero ${numero} non trovato nel database smorfia`);
      // Debug: stampa tutti i numeri disponibili
      console.log('Numeri disponibili:', Object.keys(this.smorfiaData));
      return null;
    }

    return {
      numero: parseInt(numero),
      significato: data.significato,
      simboli: data.simboli,
      descrizione: data.descrizione
    };
  }

  getAllNumbers() {
    return this.smorfiaData ? Object.keys(this.smorfiaData).map(n => parseInt(n)) : [];
  }
}