/* SMORFIA-UTILS.JS - Utilità per il nuovo formato JSON della Smorfia */

export class SmorfiaUtils {
  constructor() {
    this.smorfiaData = null;
    this.detailedSmorfiaData = null; // Nuovo: dati dettagliati per lettera
    this.loadSmorfiaData();
  }

  async loadSmorfiaData() {
    try {
      // Carica il database principale
      const response = await fetch('data/parole_smorfia_new.json');
      this.smorfiaData = await response.json();
      console.log('📚 Dati Smorfia caricati:', Object.keys(this.smorfiaData.parole_smorfia).length, 'lettere');

      // Carica i dati dettagliati per lettera
      await this.loadDetailedSmorfiaData();
    } catch (error) {
      console.error('❌ Errore caricamento dati Smorfia:', error);
    }
  }

  /**
   * Carica i file dettagliati suddivisi per lettera
   */
  async loadDetailedSmorfiaData() {
    try {
      this.detailedSmorfiaData = {};
      const letters = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'i', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'z'];

      const loadPromises = letters.map(async (letter) => {
        try {
          const response = await fetch(`data/smorfia_${letter}.json`);
          if (response.ok) {
            const data = await response.json();
            this.detailedSmorfiaData[letter.toUpperCase()] = data.elementi;
          }
        } catch (error) {
          console.warn(`⚠️ File smorfia_${letter}.json non trovato o non valido`);
        }
      });

      await Promise.all(loadPromises);

      const loadedLetters = Object.keys(this.detailedSmorfiaData).length;
      console.log(`📖 Dati dettagliati caricati per ${loadedLetters} lettere`);
    } catch (error) {
      console.error('❌ Errore caricamento dati dettagliati:', error);
    }
  }

  /**
   * Cerca una parola nella Smorfia e restituisce i numeri associati
   * @param {string} parola - La parola da cercare
   * @returns {Array} Array di numeri associati alla parola
   */
  cercaParola(parola) {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return [];
    }

    const parolaLower = parola.toLowerCase();
    const parolaCapitalized = this.capitalizeFirst(parola);

    // Cerca in tutte le lettere
    for (const lettera in this.smorfiaData.parole_smorfia) {
      if (lettera === 'metadata') continue;

      const paroleLettera = this.smorfiaData.parole_smorfia[lettera];

      // Cerca corrispondenza esatta
      if (paroleLettera[parolaCapitalized]) {
        return paroleLettera[parolaCapitalized];
      }

      // Cerca corrispondenza parziale
      for (const chiave in paroleLettera) {
        if (chiave.toLowerCase().includes(parolaLower)) {
          return paroleLettera[chiave];
        }
      }
    }

    return [];
  }

  /**
   * Cerca tutte le parole che contengono un termine
   * @param {string} termine - Il termine da cercare
   * @returns {Array} Array di oggetti {parola, numeri}
   */
  cercaTermine(termine) {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return [];
    }

    const risultati = [];
    const termineLower = termine.toLowerCase();

    for (const lettera in this.smorfiaData.parole_smorfia) {
      if (lettera === 'metadata') continue;

      const paroleLettera = this.smorfiaData.parole_smorfia[lettera];

      for (const parola in paroleLettera) {
        if (parola.toLowerCase().includes(termineLower)) {
          risultati.push({
            parola: parola,
            numeri: paroleLettera[parola],
            lettera: lettera
          });
        }
      }
    }

    return risultati.sort((a, b) => a.parola.localeCompare(b.parola));
  }

  /**
   * Ottiene tutte le parole per una lettera specifica
   * @param {string} lettera - La lettera (A, B, C, etc.)
   * @returns {Object} Oggetto con tutte le parole della lettera
   */
  getParolePerLettera(lettera) {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return {};
    }

    const letteraUpper = lettera.toUpperCase();
    return this.smorfiaData.parole_smorfia[letteraUpper] || {};
  }

  /**
   * Ottiene tutte le lettere disponibili
   * @returns {Array} Array delle lettere disponibili
   */
  getLettereDisponibili() {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return [];
    }

    return Object.keys(this.smorfiaData.parole_smorfia)
      .filter(lettera => lettera !== 'metadata')
      .sort();
  }

  /**
   * Cerca un numero e restituisce tutte le parole associate
   * @param {number} numero - Il numero da cercare (1-90)
   * @returns {Array} Array di oggetti {parola, lettera}
   */
  cercaNumero(numero) {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return [];
    }

    const risultati = [];

    for (const lettera in this.smorfiaData.parole_smorfia) {
      if (lettera === 'metadata') continue;

      const paroleLettera = this.smorfiaData.parole_smorfia[lettera];

      for (const parola in paroleLettera) {
        if (paroleLettera[parola].includes(numero)) {
          risultati.push({
            parola: parola,
            lettera: lettera,
            numeri: paroleLettera[parola]
          });
        }
      }
    }

    return risultati.sort((a, b) => a.parola.localeCompare(b.parola));
  }

  /**
   * Analizza un testo e trova tutte le parole della Smorfia
   * @param {string} testo - Il testo da analizzare
   * @returns {Array} Array di oggetti {parola, numeri, posizione}
   */
  analizzaTesto(testo) {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return [];
    }

    const risultati = [];
    const parole = testo.toLowerCase().split(/\s+/);

    parole.forEach((parola, index) => {
      // Rimuovi punteggiatura
      const parolaClean = parola.replace(/[^\w\s]/gi, '');
      const numeri = this.cercaParola(parolaClean);

      if (numeri.length > 0) {
        risultati.push({
          parola: parolaClean,
          numeri: numeri,
          posizione: index,
          parolaOriginale: parola
        });
      }
    });

    return risultati;
  }

  /**
   * Genera numeri casuali basati su parole chiave
   * @param {Array} paroleChiave - Array di parole chiave
   * @param {number} quantita - Numero di numeri da generare (default: 5)
   * @returns {Array} Array di numeri unici
   */
  generaNumeriDaParole(paroleChiave, quantita = 5) {
    const numeriTrovati = new Set();

    paroleChiave.forEach(parola => {
      const numeri = this.cercaParola(parola);
      numeri.forEach(numero => numeriTrovati.add(numero));
    });

    const numeriArray = Array.from(numeriTrovati);

    // Se non abbiamo abbastanza numeri, aggiungi numeri casuali
    while (numeriArray.length < quantita) {
      const numeroCasuale = Math.floor(Math.random() * 90) + 1;
      if (!numeriArray.includes(numeroCasuale)) {
        numeriArray.push(numeroCasuale);
      }
    }

    // Restituisci solo la quantità richiesta
    return numeriArray.slice(0, quantita).sort((a, b) => a - b);
  }

  /**
   * Ottiene statistiche sui dati della Smorfia
   * @returns {Object} Oggetto con statistiche
   */
  getStatistiche() {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return {};
    }

    let totaleParole = 0;
    const statistichePerLettera = {};

    for (const lettera in this.smorfiaData.parole_smorfia) {
      if (lettera === 'metadata') continue;

      const paroleLettera = Object.keys(this.smorfiaData.parole_smorfia[lettera]).length;
      statistichePerLettera[lettera] = paroleLettera;
      totaleParole += paroleLettera;
    }

    return {
      totaleParole,
      totaleLettere: Object.keys(statistichePerLettera).length,
      statistichePerLettera,
      metadata: this.smorfiaData.parole_smorfia.metadata
    };
  }

  /**
   * Capitalizza la prima lettera di una stringa
   * @param {string} str - La stringa da capitalizzare
   * @returns {string} Stringa con prima lettera maiuscola
   */
  capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  }

  /**
   * Cerca una parola nei dati dettagliati con varianti
   * @param {string} parola - La parola da cercare
   * @returns {Array} Array di oggetti con numeri e varianti
   */
  cercaParolaDettagliata(parola) {
    if (!this.detailedSmorfiaData) {
      console.warn('⚠️ Dati dettagliati non ancora caricati');
      return this.cercaParola(parola).map(num => ({ numero: num, varianti: [] }));
    }

    const risultati = [];
    const parolaLower = parola.toLowerCase();

    for (const lettera in this.detailedSmorfiaData) {
      const elementi = this.detailedSmorfiaData[lettera];

      elementi.forEach(elemento => {
        // Cerca nel nome principale
        if (elemento.nome.toLowerCase().includes(parolaLower)) {
          risultati.push({
            nome: elemento.nome,
            numeri: elemento.numeri,
            varianti: elemento.varianti,
            lettera: lettera,
            tipo: 'principale'
          });
        }

        // Cerca nelle varianti
        elemento.varianti.forEach(variante => {
          if (variante.nome.toLowerCase().includes(parolaLower)) {
            risultati.push({
              nome: `${elemento.nome} (${variante.nome})`,
              numeri: variante.numeri,
              varianti: [],
              lettera: lettera,
              tipo: 'variante',
              elementoPrincipale: elemento.nome
            });
          }
        });
      });
    }

    return risultati;
  }

  /**
   * Analizza un testo con i dati dettagliati per trovare match più precisi
   * @param {string} testo - Il testo da analizzare
   * @returns {Array} Array di match dettagliati
   */
  analizzaTestoDettagliato(testo) {
    if (!this.detailedSmorfiaData) {
      console.warn('⚠️ Dati dettagliati non ancora caricati, uso analisi standard');
      return this.analizzaTesto(testo);
    }

    const risultati = [];
    const testoLower = testo.toLowerCase();

    for (const lettera in this.detailedSmorfiaData) {
      const elementi = this.detailedSmorfiaData[lettera];

      elementi.forEach(elemento => {
        // Cerca match nel nome principale
        if (testoLower.includes(elemento.nome.toLowerCase())) {
          risultati.push({
            elemento: elemento.nome,
            numeri: elemento.numeri,
            tipo: 'match_principale',
            lettera: lettera,
            varianti: elemento.varianti
          });
        }

        // Cerca match nelle varianti
        elemento.varianti.forEach(variante => {
          const fraseCompleta = `${elemento.nome.toLowerCase()} ${variante.nome.toLowerCase()}`;
          if (testoLower.includes(variante.nome.toLowerCase()) ||
              testoLower.includes(fraseCompleta)) {
            risultati.push({
              elemento: `${elemento.nome} (${variante.nome})`,
              numeri: variante.numeri,
              tipo: 'match_variante',
              lettera: lettera,
              elementoPrincipale: elemento.nome
            });
          }
        });
      });
    }

    // Rimuovi duplicati e ordina per rilevanza
    const risultatiUnici = risultati.filter((item, index, self) =>
      index === self.findIndex(t => t.elemento === item.elemento)
    );

    return risultatiUnici.sort((a, b) => {
      // Priorità ai match principali
      if (a.tipo === 'match_principale' && b.tipo === 'match_variante') return -1;
      if (a.tipo === 'match_variante' && b.tipo === 'match_principale') return 1;
      return a.elemento.localeCompare(b.elemento);
    });
  }

  /**
   * Ottiene tutti i numeri possibili per un concetto con le sue varianti
   * @param {string} concetto - Il concetto da cercare
   * @returns {Object} Oggetto con numeri principali e varianti
   */
  getNumeriConVarianti(concetto) {
    const matchDettagliati = this.cercaParolaDettagliata(concetto);
    const numeriPrincipali = new Set();
    const numeriVarianti = new Set();
    const dettagli = [];

    matchDettagliati.forEach(match => {
      match.numeri.forEach(num => {
        if (match.tipo === 'principale') {
          numeriPrincipali.add(num);
        } else {
          numeriVarianti.add(num);
        }
      });

      dettagli.push({
        nome: match.nome,
        numeri: match.numeri,
        tipo: match.tipo,
        varianti: match.varianti
      });
    });

    return {
      numeriPrincipali: Array.from(numeriPrincipali).sort((a, b) => a - b),
      numeriVarianti: Array.from(numeriVarianti).sort((a, b) => a - b),
      tuttiINumeri: Array.from(new Set([...numeriPrincipali, ...numeriVarianti])).sort((a, b) => a - b),
      dettagli: dettagli
    };
  }

  /**
   * Verifica se i dati sono caricati
   * @returns {boolean} True se i dati sono caricati
   */
  isDataLoaded() {
    return this.smorfiaData !== null;
  }

  /**
   * Verifica se i dati dettagliati sono caricati
   * @returns {boolean} True se i dati dettagliati sono caricati
   */
  isDetailedDataLoaded() {
    return this.detailedSmorfiaData !== null && Object.keys(this.detailedSmorfiaData).length > 0;
  }
}

// Esporta un'istanza globale
export const smorfiaUtils = new SmorfiaUtils();
